import { Card, CardContent } from "@/components/ui/card"

const testimonials = [
  {
    quote:
      "This platform transformed how we prepare our students for the industry. The real-world training sessions have significantly improved our placement rates.",
    name: "Dr. <PERSON>",
    role: "Dean of Computer Science",
    company: "Tech University",
  },
  {
    quote:
      "Contributing to student education while expanding my professional network has been incredibly rewarding. The platform makes it easy to share knowledge.",
    name: "<PERSON>",
    role: "Senior Software Engineer",
    company: "TechCorp Solutions",
  },
  {
    quote:
      "The hands-on training I received through this platform gave me the confidence and skills I needed to land my dream job right after graduation.",
    name: "<PERSON><PERSON>",
    role: "Recent Graduate",
    company: "Now at Google",
  },
]

export function Testimonials() {
  return (
    <section className="py-24 bg-background">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6 text-balance">What Our Community Says</h2>
          <p className="text-xl text-muted-foreground text-pretty leading-relaxed">
            Real stories from real people making a difference
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="bg-card border-border">
              <CardContent className="p-8">
                <blockquote className="text-card-foreground mb-6 text-pretty leading-relaxed">
                  "{testimonial.quote}"
                </blockquote>
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-semibold">
                    {testimonial.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </div>
                  <div>
                    <div className="font-semibold text-card-foreground">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.company}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
