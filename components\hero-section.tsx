import { Button } from "@/components/ui/button"

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <img src="/abstract-tech-education-network-pattern.jpg" alt="" className="w-full h-full object-cover" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-7xl font-bold text-foreground mb-6 text-balance">
            Connecting IT Professionals with Colleges to <span className="text-primary">Bridge the Skill Gap</span>
          </h1>

          <p className="text-xl md:text-2xl text-muted-foreground mb-12 text-pretty leading-relaxed">
            Real industry training for students. Real impact for professionals.
          </p>

          {/* Call-to-action buttons with Apple styling */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <Button className="btn-primary text-lg px-12 py-5">
              Register as a College
            </Button>
            <Button className="btn-outline text-lg px-12 py-5">
              Join as IT Professional
            </Button>
          </div>

          {/* Trust indicators - Social proof */}
          <div className="flex flex-wrap justify-center items-center gap-8 text-footnote text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="w-1 h-1 bg-primary rounded-full"></div>
              <span>95% Student Satisfaction</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1 h-1 bg-secondary rounded-full"></div>
              <span>10,000+ Students Trained</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1 h-1 bg-accent rounded-full"></div>
              <span>Industry-Validated Curriculum</span>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="w-6 h-10 border-2 border-muted-foreground rounded-full flex justify-center">
          <div className="w-1 h-3 bg-muted-foreground rounded-full mt-2 animate-bounce"></div>
        </div>
      </div>
    </section>
  )
}
