const stats = [
  { number: "500+", label: "IT Professionals Onboarded" },
  { number: "200+", label: "Colleges Connected" },
  { number: "10,000+", label: "Students Trained" },
  { number: "95%", label: "Student Satisfaction Rate" },
]

export function ImpactStats() {
  return (
    <section className="py-24 bg-primary">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-primary-foreground mb-6 text-balance">Our Impact</h2>
          <p className="text-xl text-primary-foreground/80 text-pretty leading-relaxed">
            Measurable results in bridging the skill gap
          </p>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-4xl md:text-6xl font-bold text-primary-foreground mb-2">{stat.number}</div>
              <p className="text-primary-foreground/80 text-lg text-balance">{stat.label}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
