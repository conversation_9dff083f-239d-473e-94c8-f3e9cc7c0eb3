export function Footer() {
  return (
    <footer className="py-16 bg-foreground text-background">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl font-bold mb-4">SkillBridge</h3>
            <p className="text-background/80 text-pretty leading-relaxed mb-4">
              Connecting IT professionals with colleges to bridge the skill gap and create industry-ready graduates.
            </p>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2 text-background/80">
              <li>
                <a href="#" className="hover:text-background transition-colors">
                  About
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-background transition-colors">
                  Contact
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-background transition-colors">
                  Privacy Policy
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-background transition-colors">
                  Terms of Service
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Connect</h4>
            <div className="flex gap-4">
              <a href="#" className="text-background/80 hover:text-background transition-colors">
                LinkedIn
              </a>
              <a href="#" className="text-background/80 hover:text-background transition-colors">
                Twitter
              </a>
              <a href="#" className="text-background/80 hover:text-background transition-colors">
                Instagram
              </a>
            </div>
          </div>
        </div>

        <div className="border-t border-background/20 pt-8 text-center text-background/60">
          <p>&copy; 2024 SkillBridge. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
