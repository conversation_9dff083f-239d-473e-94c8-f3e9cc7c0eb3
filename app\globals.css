@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Apple-inspired transparent design system */
  --background: rgba(255, 255, 255, 0.8);
  --foreground: #1d1d1f;
  --card: rgba(255, 255, 255, 0.6);
  --card-foreground: #1d1d1f;
  --popover: rgba(255, 255, 255, 0.95);
  --popover-foreground: #1d1d1f;
  --primary: #007aff;
  --primary-foreground: #ffffff;
  --secondary: #5856d6;
  --secondary-foreground: #ffffff;
  --muted: rgba(142, 142, 147, 0.12);
  --muted-foreground: #8e8e93;
  --accent: #ff9500;
  --accent-foreground: #ffffff;
  --destructive: #ff3b30;
  --destructive-foreground: #ffffff;
  --border: rgba(0, 0, 0, 0.1);
  --input: rgba(142, 142, 147, 0.12);
  --ring: rgba(0, 122, 255, 0.3);
  --chart-1: #007aff;
  --chart-2: #5856d6;
  --chart-3: #ff9500;
  --chart-4: #34c759;
  --chart-5: #ff3b30;
  --radius: 12px;
  --sidebar: rgba(255, 255, 255, 0.8);
  --sidebar-foreground: #1d1d1f;
  --sidebar-primary: #007aff;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #5856d6;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: rgba(0, 0, 0, 0.1);
  --sidebar-ring: rgba(0, 122, 255, 0.3);
  
  /* Glassmorphism variables */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* Apple typography scale */
  --text-display: 80px;
  --text-large-title: 34px;
  --text-title-1: 28px;
  --text-title-2: 22px;
  --text-title-3: 20px;
  --text-headline: 17px;
  --text-body: 17px;
  --text-callout: 16px;
  --text-subhead: 15px;
  --text-footnote: 13px;
  --text-caption-1: 12px;
  --text-caption-2: 11px;
}

.dark {
  /* Apple-inspired dark mode with transparency */
  --background: rgba(0, 0, 0, 0.8);
  --foreground: #f5f5f7;
  --card: rgba(28, 28, 30, 0.6);
  --card-foreground: #f5f5f7;
  --popover: rgba(28, 28, 30, 0.95);
  --popover-foreground: #f5f5f7;
  --primary: #0a84ff;
  --primary-foreground: #000000;
  --secondary: #5e5ce6;
  --secondary-foreground: #ffffff;
  --muted: rgba(142, 142, 147, 0.12);
  --muted-foreground: #8e8e93;
  --accent: #ff9f0a;
  --accent-foreground: #000000;
  --destructive: #ff453a;
  --destructive-foreground: #ffffff;
  --border: rgba(255, 255, 255, 0.1);
  --input: rgba(142, 142, 147, 0.12);
  --ring: rgba(10, 132, 255, 0.3);
  --chart-1: #0a84ff;
  --chart-2: #5e5ce6;
  --chart-3: #ff9f0a;
  --chart-4: #30d158;
  --chart-5: #ff453a;
  --sidebar: rgba(28, 28, 30, 0.8);
  --sidebar-foreground: #f5f5f7;
  --sidebar-primary: #0a84ff;
  --sidebar-primary-foreground: #000000;
  --sidebar-accent: #5e5ce6;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: rgba(10, 132, 255, 0.3);
  
  /* Dark mode glassmorphism */
  --glass-bg: rgba(28, 28, 30, 0.25);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
