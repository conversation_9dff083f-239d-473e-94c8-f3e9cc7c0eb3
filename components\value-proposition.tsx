import { Card, CardContent } from "@/components/ui/card"

const benefits = [
  {
    icon: "📚",
    title: "Real-world training for students",
    description:
      "Bridge the gap between academic theory and industry practice with hands-on training from experienced professionals.",
  },
  {
    icon: "💼",
    title: "Industry exposure and mentorship",
    description: "Connect students directly with IT professionals for mentorship, networking, and career guidance.",
  },
  {
    icon: "🏫",
    title: "Stronger placement opportunities",
    description: "Enhance your college's reputation with improved student outcomes and industry partnerships.",
  },
  {
    icon: "🚀",
    title: "Recognition and networking",
    description:
      "IT professionals gain recognition, expand their network, and contribute to the next generation of talent.",
  },
]

export function ValueProposition() {
  return (
    <section className="py-24 bg-background">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6 text-balance">Why This Platform?</h2>
          <p className="text-xl text-muted-foreground text-pretty leading-relaxed">
            Transforming education through industry collaboration
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => (
            <Card key={index} className="bg-card border-border hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-8 text-center">
                <div className="text-4xl mb-4">{benefit.icon}</div>
                <h3 className="text-xl font-semibold text-card-foreground mb-4 text-balance">{benefit.title}</h3>
                <p className="text-muted-foreground text-pretty leading-relaxed">{benefit.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
