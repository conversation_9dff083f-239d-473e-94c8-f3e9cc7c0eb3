const steps = [
  {
    number: "01",
    title: "Register",
    description: "College or IT professional registers on our platform with their expertise and requirements.",
  },
  {
    number: "02",
    title: "Match",
    description:
      "Our intelligent matchmaking system connects colleges with relevant IT professionals based on domain expertise.",
  },
  {
    number: "03",
    title: "Train",
    description:
      "Real training sessions are delivered online or offline, bringing industry knowledge to the classroom.",
  },
  {
    number: "04",
    title: "Impact",
    description: "Students gain real-world project experience and industry-ready skills for better career outcomes.",
  },
]

export function HowItWorks() {
  return (
    <section className="py-24 bg-muted">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6 text-balance">How It Works</h2>
          <p className="text-xl text-muted-foreground text-pretty leading-relaxed">
            Simple steps to transform education
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6">
                {step.number}
              </div>
              <h3 className="text-2xl font-semibold text-foreground mb-4">{step.title}</h3>
              <p className="text-muted-foreground text-pretty leading-relaxed">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
