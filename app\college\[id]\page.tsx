import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  MapPin,
  Calendar,
  Users,
  GraduationCap,
  Award,
  Globe,
  Phone,
  Mail,
  Star,
  Wifi,
  Monitor,
  Building,
  Bed,
  Share2,
  Flag,
  CheckCircle,
} from "lucide-react";
import Image from "next/image";

// Mock data - in real app this would come from API/database
const collegeData = {
  id: "mit-college-pune",
  name: "MIT College of Engineering",
  acronym: "MITCOE",
  tagline: "Excellence in Engineering Education",
  isVerified: true,
  logo: "/assets/college-logos/mit-logo.png",
  coverImage: "/assets/college-covers/mit-cover.jpg",
  location: {
    city: "Pune",
    state: "Maharashtra",
    country: "India",
  },
  stats: {
    established: 1983,
    students: 3500,
    faculty: 280,
    courses: 45,
    accreditations: ["NAAC A+", "AICTE", "NBA"],
  },
  overview: {
    description:
      "MIT College of Engineering is a premier engineering institution affiliated with Pune University, known for its excellence in technical education and industry partnerships.",
    affiliation: "Pune University",
    website: "https://mitcoe.ac.in",
    contact: {
      email: "<EMAIL>",
      phone: "+91-20-2734-3001",
    },
  },
  academics: {
    departments: [
      "Computer Science & Engineering",
      "Information Technology",
      "Electronics & Telecommunication",
      "Mechanical Engineering",
      "Civil Engineering",
      "Electrical Engineering",
    ],
    programs: [
      {
        name: "B.Tech Computer Science",
        type: "Undergraduate",
        duration: "4 years",
        intake: 120,
      },
      {
        name: "B.Tech Information Technology",
        type: "Undergraduate",
        duration: "4 years",
        intake: 60,
      },
      {
        name: "M.Tech Computer Science",
        type: "Postgraduate",
        duration: "2 years",
        intake: 24,
      },
      {
        name: "MBA Technology Management",
        type: "Postgraduate",
        duration: "2 years",
        intake: 60,
      },
    ],
    specializations: [
      "Cloud Computing",
      "Artificial Intelligence",
      "Cyber Security",
      "Data Science",
      "IoT",
    ],
  },
  infrastructure: {
    computerLabs: 12,
    pcsPerLab: 40,
    internetBandwidth: "1 Gbps",
    equipment: [
      "Smart Boards",
      "Projectors",
      "Raspberry Pi Kits",
      "Arduino Boards",
      "3D Printers",
    ],
    labAvailability: true,
    classrooms: 45,
    avFacilities: true,
    accommodation: true,
  },
  ratings: {
    average: 4.3,
    totalReviews: 127,
    reviews: [
      {
        rating: 5,
        comment: "Excellent faculty and industry exposure",
        author: "Anonymous IT Professional",
      },
      {
        rating: 4,
        comment: "Good infrastructure and placement support",
        author: "Anonymous IT Professional",
      },
      {
        rating: 4,
        comment: "Strong technical curriculum and practical learning",
        author: "Anonymous IT Professional",
      },
    ],
  },
};

export default function CollegeProfilePage({
  params,
}: {
  params: { id: string };
}) {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <div className="relative h-80 overflow-hidden">
        <Image
          src="/assets/college-covers/mit-cover.jpg"
          alt="College Campus"
          fill
          className="object-fill"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />

        {/* College Header Info */}
        <div className="absolute bottom-0 left-0 right-0 p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-end gap-6">
              <div className="glass-card rounded-xl">
                <Image
                  src="/assets/college-logos/mit-logo.png"
                  alt={collegeData.name}
                  width={150}
                  height={100}
                  className="rounded-lg"
                />
              </div>

              <div className="flex-1 text-white">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-title-1 font-bold">{collegeData.name}</h1>
                  {collegeData.isVerified && (
                    <Badge
                      variant="secondary"
                      className="bg-primary text-primary-foreground"
                    >
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Verified
                    </Badge>
                  )}
                </div>
                <p className="text-callout opacity-90 mb-2">
                  {collegeData.tagline}
                </p>
                <div className="flex items-center gap-2 text-subhead opacity-80">
                  <MapPin className="w-4 h-4" />
                  <span>
                    {collegeData.location.city}, {collegeData.location.state},{" "}
                    {collegeData.location.country}
                  </span>
                </div>
              </div>

              <div className="flex gap-3">
                <Button size="lg" className="bg-primary hover:bg-primary/90">
                  Request Training
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="glass-card border-white/20 text-white hover:bg-white/10 bg-transparent"
                >
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="glass-card border-white/20 text-white hover:bg-white/10 bg-transparent"
                >
                  <Flag className="w-4 h-4 mr-2" />
                  Report
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
          <Card className="text-center">
            <CardContent className="p-4">
              <Calendar className="w-6 h-6 mx-auto mb-2 text-primary" />
              <div className="text-title-3 font-bold">
                {collegeData.stats.established}
              </div>
              <div className="text-caption-1 text-muted-foreground">
                Established
              </div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-4">
              <Users className="w-6 h-6 mx-auto mb-2 text-primary" />
              <div className="text-title-3 font-bold">
                {collegeData.stats.students.toLocaleString()}
              </div>
              <div className="text-caption-1 text-muted-foreground">
                Students
              </div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-4">
              <GraduationCap className="w-6 h-6 mx-auto mb-2 text-primary" />
              <div className="text-title-3 font-bold">
                {collegeData.stats.faculty}
              </div>
              <div className="text-caption-1 text-muted-foreground">
                Faculty
              </div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-4">
              <Award className="w-6 h-6 mx-auto mb-2 text-primary" />
              <div className="text-title-3 font-bold">
                {collegeData.stats.courses}
              </div>
              <div className="text-caption-1 text-muted-foreground">
                Courses
              </div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-4">
              <CheckCircle className="w-6 h-6 mx-auto mb-2 text-primary" />
              <div className="flex flex-wrap gap-1 justify-center">
                {collegeData.stats.accreditations.map((acc) => (
                  <Badge
                    key={acc}
                    variant="secondary"
                    className="text-caption-2"
                  >
                    {acc}
                  </Badge>
                ))}
              </div>
              <div className="text-caption-1 text-muted-foreground mt-1">
                Accredited
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Overview Section */}
            <Card>
              <CardHeader>
                <CardTitle className="text-title-2">Overview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-body leading-relaxed">
                  {collegeData.overview.description}
                </p>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-subhead font-medium mb-1">
                      Affiliation
                    </div>
                    <div className="text-body">
                      {collegeData.overview.affiliation}
                    </div>
                  </div>
                  <div>
                    <div className="text-subhead font-medium mb-1">
                      Official Website
                    </div>
                    <a
                      href={collegeData.overview.website}
                      className="text-primary hover:underline text-body"
                    >
                      <Globe className="w-4 h-4 inline mr-1" />
                      Visit Website
                    </a>
                  </div>
                </div>

                <Separator />

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-muted-foreground" />
                    <span className="text-body">
                      {collegeData.overview.contact.email}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-muted-foreground" />
                    <span className="text-body">
                      {collegeData.overview.contact.phone}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Academics & Programs */}
            <Card>
              <CardHeader>
                <CardTitle className="text-title-2">
                  Academics & Programs
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="text-headline font-medium mb-3">
                    Departments
                  </h4>
                  <div className="grid md:grid-cols-2 gap-2">
                    {collegeData.academics.departments.map((dept) => (
                      <div
                        key={dept}
                        className="flex items-center gap-2 p-2 rounded-lg bg-muted/50"
                      >
                        <GraduationCap className="w-4 h-4 text-primary" />
                        <span className="text-callout">{dept}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="text-headline font-medium mb-3">
                    Key Programs
                  </h4>
                  <div className="space-y-3">
                    {collegeData.academics.programs.map((program) => (
                      <div
                        key={program.name}
                        className="flex justify-between items-center p-3 rounded-lg border"
                      >
                        <div>
                          <div className="text-callout font-medium">
                            {program.name}
                          </div>
                          <div className="text-footnote text-muted-foreground">
                            {program.type} • {program.duration}
                          </div>
                        </div>
                        <Badge variant="outline">{program.intake} seats</Badge>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="text-headline font-medium mb-3">
                    Specializations
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {collegeData.academics.specializations.map((spec) => (
                      <Badge key={spec} variant="secondary">
                        {spec}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Infrastructure & Facilities */}
            <Card>
              <CardHeader>
                <CardTitle className="text-title-2">
                  Infrastructure & Facilities
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Monitor className="w-5 h-5 text-primary" />
                      <div>
                        <div className="text-callout font-medium">
                          {collegeData.infrastructure.computerLabs} Computer
                          Labs
                        </div>
                        <div className="text-footnote text-muted-foreground">
                          {collegeData.infrastructure.pcsPerLab} PCs per lab
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <Wifi className="w-5 h-5 text-primary" />
                      <div>
                        <div className="text-callout font-medium">
                          Internet Connectivity
                        </div>
                        <div className="text-footnote text-muted-foreground">
                          {collegeData.infrastructure.internetBandwidth}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <Building className="w-5 h-5 text-primary" />
                      <div>
                        <div className="text-callout font-medium">
                          {collegeData.infrastructure.classrooms} Classrooms
                        </div>
                        <div className="text-footnote text-muted-foreground">
                          With AV facilities
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <Bed className="w-5 h-5 text-primary" />
                      <div>
                        <div className="text-callout font-medium">
                          Accommodation
                        </div>
                        <div className="text-footnote text-muted-foreground">
                          {collegeData.infrastructure.accommodation
                            ? "Available"
                            : "Not Available"}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-callout font-medium mb-3">
                      Available Equipment
                    </h5>
                    <div className="space-y-2">
                      {collegeData.infrastructure.equipment.map((item) => (
                        <div key={item} className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-primary" />
                          <span className="text-footnote">{item}</span>
                        </div>
                      ))}
                    </div>

                    <div className="mt-4 p-3 rounded-lg bg-primary/10">
                      <div className="text-callout font-medium text-primary">
                        Lab Training Available
                      </div>
                      <div className="text-footnote text-muted-foreground">
                        Facilities available for external training programs
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Ratings & Reviews */}
            <Card>
              <CardHeader>
                <CardTitle className="text-title-3">
                  Ratings & Reviews
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-title-1 font-bold">
                    {collegeData.ratings.average}
                  </div>
                  <div className="flex justify-center gap-1 my-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`w-4 h-4 ${
                          star <= collegeData.ratings.average
                            ? "fill-yellow-400 text-yellow-400"
                            : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <div className="text-footnote text-muted-foreground">
                    Based on {collegeData.ratings.totalReviews} reviews
                  </div>
                </div>

                <Separator />

                <div className="space-y-3">
                  {collegeData.ratings.reviews.map((review, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="flex gap-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`w-3 h-3 ${
                                star <= review.rating
                                  ? "fill-yellow-400 text-yellow-400"
                                  : "text-gray-300"
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                      <p className="text-footnote leading-relaxed">
                        {review.comment}
                      </p>
                      <div className="text-caption-2 text-muted-foreground">
                        - {review.author}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-title-3">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" size="lg">
                  Request Training Program
                </Button>
                <Button variant="outline" className="w-full bg-transparent">
                  Download Brochure
                </Button>
                <Button variant="outline" className="w-full bg-transparent">
                  Contact Admissions
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
