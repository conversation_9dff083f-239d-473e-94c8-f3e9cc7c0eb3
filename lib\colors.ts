/**
 * Minimalistic and elegant color configuration
 * Following a refined neutral palette with subtle accent
 */

export const colors = {
  // Primary brand color - sophisticated slate
  primary: {
    50: "#f8fafc",
    100: "#f1f5f9",
    200: "#e2e8f0",
    300: "#cbd5e1",
    400: "#94a3b8",
    500: "#64748b",
    600: "#475569",
    700: "#334155",
    800: "#1e293b",
    900: "#0f172a",
  },

  // Neutral grays - warm undertones
  neutral: {
    50: "#fafafa",
    100: "#f5f5f5",
    200: "#e5e5e5",
    300: "#d4d4d4",
    400: "#a3a3a3",
    500: "#737373",
    600: "#525252",
    700: "#404040",
    800: "#262626",
    900: "#171717",
  },

  // Accent color - subtle emerald for highlights
  accent: {
    50: "#ecfdf5",
    100: "#d1fae5",
    200: "#a7f3d0",
    300: "#6ee7b7",
    400: "#34d399",
    500: "#10b981",
    600: "#059669",
    700: "#047857",
    800: "#065f46",
    900: "#064e3b",
  },

  // Semantic colors
  success: "#10b981",
  warning: "#f59e0b",
  error: "#ef4444",
  info: "#3b82f6",

  // Pure colors for absolute contrast
  white: "#ffffff",
  black: "#000000",
} as const

export type ColorScale = typeof colors.primary
export type Colors = typeof colors

// Helper function to get color values
export const getColor = (colorPath: string): string => {
  const keys = colorPath.split(".")
  let value: any = colors

  for (const key of keys) {
    value = value[key]
  }

  return value || colorPath
}

// Common color combinations for consistent usage
export const colorCombinations = {
  // Light theme combinations
  light: {
    background: colors.white,
    surface: colors.neutral[50],
    text: {
      primary: colors.neutral[900],
      secondary: colors.neutral[600],
      muted: colors.neutral[400],
    },
    border: colors.neutral[200],
    accent: colors.accent[500],
  },

  // Dark theme combinations
  dark: {
    background: colors.neutral[900],
    surface: colors.neutral[800],
    text: {
      primary: colors.neutral[50],
      secondary: colors.neutral[300],
      muted: colors.neutral[500],
    },
    border: colors.neutral[700],
    accent: colors.accent[400],
  },
} as const
